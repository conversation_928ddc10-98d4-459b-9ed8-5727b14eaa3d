import React from "react";
import Image from "next/image";
import { SparklesIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";

// Stat item interface
interface StatItem {
  number: string;
  label: string;
  color: string;
  hoverColor: string;
  textColor: string;
}

// Stat square component
const StatSquare: React.FC<StatItem> = ({
  number,
  label,
  color,
  hoverColor,
  textColor,
}) => (
  <div className="group flex flex-col items-center">
    <div
      className={`w-20 h-20 rounded-xl ${color} ${hoverColor} transition-all duration-300 flex flex-col items-center justify-center shadow-lg`}
    >
      <span className={`text-2xl font-bold ${textColor}`}>{number}</span>
    </div>
    <span className="mt-3 text-lg font-medium text-neutral-700 dark:text-neutral-300">
      {label}
    </span>
  </div>
);

// Stats data
const statsData: StatItem[] = [
  {
    number: "17",
    label: "Years Experience",
    color: "bg-orange-600",
    hoverColor: "group-hover:bg-orange-700",
    textColor: "text-white",
  },
  {
    number: "5+",
    label: "Championship Titles",
    color: "bg-yellow-500",
    hoverColor: "group-hover:bg-yellow-600",
    textColor: "text-yellow-900",
  },
  {
    number: "2",
    label: "Education Degrees",
    color: "bg-blue-500",
    hoverColor: "group-hover:bg-blue-600",
    textColor: "text-white",
  },
  {
    number: "100+",
    label: "Trained Students",
    color: "bg-emerald-500",
    hoverColor: "group-hover:bg-emerald-600",
    textColor: "text-white",
  },
];

// Stats grid component
const StatsGrid: React.FC = () => (
  <div className="grid grid-cols-2 md:grid-cols-4 gap-8 justify-items-center">
    {statsData.map((stat, index) => (
      <StatSquare key={index} {...stat} />
    ))}
  </div>
);

interface AboutIvanProps {
  title?: string;
  subtitle?: string;
}

const AboutIvan: React.FC<AboutIvanProps> = ({
  title = "Meet Ivan",
  subtitle = "Your Professional Boxing Coach",
}) => {
  return (
    <section id="about" className="w-full py-20 bg-white dark:bg-black">
      {/* Header Section */}
      <div className="text-center mb-16 px-6">
        <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
          {title}
        </h2>
        <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
          {subtitle} - Master of Sport in Kickboxing with 17 years of experience
          training fighters at all levels
        </p>
      </div>

      {/* Full Width Content */}
      <div className="w-full">
        {/* Badge Section */}
        <div className="flex justify-center mb-12">
          <Badge
            variant="outline"
            className="rounded-[14px] border border-black/10 bg-white text-base dark:border-white/5 dark:bg-neutral-800/5"
          >
            <SparklesIcon className="fill-[#EEBDE0] stroke-1 text-neutral-800 mr-2" />
            Ivan&apos;s Achievements
          </Badge>
        </div>

        {/* Two Column Layout */}
        <div className="grid md:grid-cols-2 gap-12 px-6 max-w-7xl mx-auto">
          {/* Left side - Stats */}
          <div className="flex items-center justify-center">
            <div className="w-full max-w-lg">
              <StatsGrid />
            </div>
          </div>

          {/* Right side - Photo */}
          <div className="flex items-center justify-center">
            <div className="relative w-full max-w-md">
              <Image
                src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=500&h=600&fit=crop&crop=center"
                alt="Ivan - Professional Boxing Coach"
                width={500}
                height={600}
                className="rounded-2xl object-cover shadow-lg w-full h-auto"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutIvan;
