import React from "react";
import Image from "next/image";
import { SparklesIcon } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import FlipLink from "@/components/ui/text-effect-flipper";

// Boxing achievement icons with numbers only
const BoxingIcons = {
  experience: (props: any) => (
    <svg
      width="86"
      height="86"
      viewBox="0 0 86 86"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="86"
        height="86"
        rx="14"
        fill="#ea580c"
        className="fill-orange-600 transition-all duration-500 ease-in-out group-hover:fill-orange-700"
      />
      <text
        x="43"
        y="50"
        textAnchor="middle"
        className="fill-white text-3xl font-bold"
      >
        17
      </text>
    </svg>
  ),
  titles: (props: any) => (
    <svg
      width="86"
      height="86"
      viewBox="0 0 86 86"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="86"
        height="86"
        rx="14"
        fill="#eab308"
        className="fill-yellow-500 transition-all duration-500 ease-in-out group-hover:fill-yellow-600"
      />
      <text
        x="43"
        y="50"
        textAnchor="middle"
        className="fill-yellow-900 text-3xl font-bold"
      >
        5+
      </text>
    </svg>
  ),
  education: (props: any) => (
    <svg
      width="86"
      height="86"
      viewBox="0 0 86 86"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="86"
        height="86"
        rx="14"
        fill="#3b82f6"
        className="fill-blue-500 transition-all duration-500 ease-in-out group-hover:fill-blue-600"
      />
      <text
        x="43"
        y="50"
        textAnchor="middle"
        className="fill-white text-3xl font-bold"
      >
        2
      </text>
    </svg>
  ),
  students: (props: any) => (
    <svg
      width="86"
      height="86"
      viewBox="0 0 86 86"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect
        width="86"
        height="86"
        rx="14"
        fill="#10b981"
        className="fill-emerald-500 transition-all duration-500 ease-in-out group-hover:fill-emerald-600"
      />
      <text
        x="43"
        y="50"
        textAnchor="middle"
        className="fill-white text-2xl font-bold"
      >
        100+
      </text>
    </svg>
  ),
};

interface AboutIvanProps {
  title?: string;
  subtitle?: string;
}

export function AboutIvan({
  title = "Meet Ivan",
  subtitle = "Your Professional Boxing Coach",
}: AboutIvanProps) {
  return (
    <section id="about" className="w-full py-20 bg-white dark:bg-black">
      {/* Header Section */}
      <div className="text-center mb-16 px-6">
        <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
          {title}
        </h2>
        <p className="text-xl text-neutral-600 dark:text-neutral-400 max-w-3xl mx-auto">
          {subtitle} - Master of Sport in Kickboxing with 17 years of experience
          training fighters at all levels
        </p>
      </div>

      {/* Full Width Content - MouseTrailDemo Pattern */}
      <div className="grid md:grid-cols-2 gap-12 w-full">
        {/* Left side - Stats with MouseTrailDemo pattern */}
        <section className="h-4xl mx-auto my-12 w-full max-w-4xl rounded-[24px] border border-black/5 p-2 shadow-sm dark:border-white/5 md:rounded-t-[44px]">
          <div className="relative mx-auto w-full rounded-[24px] border border-black/5 bg-neutral-800/5 shadow-sm dark:border-white/5 md:gap-8 md:rounded-b-[20px] md:rounded-t-[40px]">
            <article className="z-50 mt-20 flex flex-col items-center justify-center">
              <Badge
                variant="outline"
                className="mb-3 rounded-[14px] border border-black/10 bg-white text-base dark:border-white/5 dark:bg-neutral-800/5 md:left-6"
              >
                <SparklesIcon className="fill-[#EEBDE0] stroke-1 text-neutral-800" />{" "}
                Ivan&apos;s Achievements
              </Badge>
            </article>
            <section className="h-full">
              <section className="grid place-content-center gap-2 px-8 py-24 text-black">
                <div className="group flex items-center justify-center">
                  <BoxingIcons.experience />
                  <FlipLink href="#about">Years Experience</FlipLink>
                </div>
                <div className="group flex items-center justify-center">
                  <FlipLink href="#about">Championship Titles</FlipLink>
                  <BoxingIcons.titles />
                </div>
                <div className="group flex items-center justify-center">
                  <BoxingIcons.education />
                  <FlipLink href="#about">Education Degrees</FlipLink>
                </div>
                <div className="group flex items-center justify-center">
                  <FlipLink href="#about">Trained Students</FlipLink>
                  <BoxingIcons.students />
                </div>
              </section>
            </section>
          </div>
        </section>

        {/* Right side - Photo */}
        <div className="flex items-center justify-center px-6">
          <div className="relative w-full max-w-md">
            <Image
              src="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=500&h=600&fit=crop&crop=center"
              alt="Ivan - Professional Boxing Coach"
              width={500}
              height={600}
              className="rounded-2xl object-cover shadow-lg w-full h-auto"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default AboutIvan;
